import axios from "axios";
import dotenv from "dotenv";

dotenv.config();

/**
 * BinanceAPI Class - Handles all Binance API interactions
 * Extracted from index.js to improve code organization and maintainability
 */
export class BinanceAPI {
  constructor(options = {}) {
    // API Configuration
    this.apiUrl = options.apiUrl || "https://api.binance.com/api/v3/klines";
    this.symbol = options.symbol || process.env.SYMBOL || "ETHUSDT";
    this.defaultInterval = options.defaultInterval || process.env.INTERVAL || "1h";
    
    // Multi-Timeframe Configuration
    this.timeframes = options.timeframes || {
      '15m': { interval: '15m', limit: 96, name: '15-minute' },   // 24 hours of 15m candles
      '1h': { interval: '1h', limit: 168, name: '1-hour' },      // 7 days of 1h candles
      '4h': { interval: '4h', limit: 180, name: '4-hour' }       // 30 days of 4h candles
    };
    
    // Data cache for multi-timeframe analysis
    this.dataCache = {
      '15m': { data: null, lastUpdate: 0, ttl: 15 * 60 * 1000 },  // 15 min TTL
      '1h': { data: null, lastUpdate: 0, ttl: 60 * 60 * 1000 },   // 1 hour TTL
      '4h': { data: null, lastUpdate: 0, ttl: 4 * 60 * 60 * 1000 } // 4 hour TTL
    };
  }

  /**
   * Fetch data for a specific timeframe
   * @param {string} timeframe - The timeframe to fetch (15m, 1h, 4h)
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchTimeframeData(timeframe) {
    const config = this.timeframes[timeframe];
    if (!config) {
      throw new Error(`Invalid timeframe: ${timeframe}`);
    }

    try {
      const { data } = await axios.get(this.apiUrl, {
        params: {
          symbol: this.symbol,
          interval: config.interval,
          limit: config.limit
        },
      });

      return data.map((d) => ({
        time: d[0],
        open: parseFloat(d[1]),
        high: parseFloat(d[2]),
        low: parseFloat(d[3]),
        close: parseFloat(d[4]),
        volume: parseFloat(d[5]),
        closeTime: d[6],
        timeframe: timeframe
      }));
    } catch (error) {
      console.error(`Error fetching ${timeframe} data:`, error.message);
      throw error;
    }
  }

  /**
   * Get cached data for a timeframe, fetch fresh if cache is expired
   * @param {string} timeframe - The timeframe to get data for
   * @returns {Promise<Array>} Array of candle data
   */
  async getCachedData(timeframe) {
    const cache = this.dataCache[timeframe];
    const now = Date.now();

    // Check if cache is valid
    if (cache.data && (now - cache.lastUpdate) < cache.ttl) {
      console.log(`Using cached data for ${timeframe}`);
      return cache.data;
    }

    // Fetch fresh data
    console.log(`Fetching fresh data for ${timeframe}`);
    const data = await this.fetchTimeframeData(timeframe);

    // Update cache
    cache.data = data;
    cache.lastUpdate = now;

    return data;
  }

  /**
   * Fetch all timeframes data concurrently
   * @returns {Promise<Object>} Object containing data for all timeframes
   */
  async fetchAllTimeframes() {
    try {
      const [data15m, data1h, data4h] = await Promise.all([
        this.getCachedData('15m'),
        this.getCachedData('1h'),
        this.getCachedData('4h')
      ]);

      return {
        '15m': data15m,
        '1h': data1h,
        '4h': data4h
      };
    } catch (error) {
      console.error('Error fetching multi-timeframe data:', error);
      throw error;
    }
  }

  /**
   * Legacy fetchData function for backward compatibility
   * @param {string} interval - Optional interval override
   * @param {number} limit - Optional limit override
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchData(interval = null, limit = 240) {
    const { data } = await axios.get(this.apiUrl, {
      params: { 
        symbol: this.symbol, 
        interval: interval || this.defaultInterval, 
        limit: limit 
      },
    });

    return data.map((d) => ({
      time: d[0],
      open: parseFloat(d[1]),
      high: parseFloat(d[2]),
      low: parseFloat(d[3]),
      close: parseFloat(d[4]),
      volume: parseFloat(d[5]),
      closeTime: d[6],
    }));
  }

  /**
   * Clear cache for a specific timeframe or all timeframes
   * @param {string} timeframe - Optional specific timeframe to clear
   */
  clearCache(timeframe = null) {
    if (timeframe) {
      if (this.dataCache[timeframe]) {
        this.dataCache[timeframe].data = null;
        this.dataCache[timeframe].lastUpdate = 0;
        console.log(`Cache cleared for ${timeframe}`);
      }
    } else {
      Object.keys(this.dataCache).forEach(tf => {
        this.dataCache[tf].data = null;
        this.dataCache[tf].lastUpdate = 0;
      });
      console.log('All cache cleared');
    }
  }

  /**
   * Get cache status for all timeframes
   * @returns {Object} Cache status information
   */
  getCacheStatus() {
    const now = Date.now();
    const status = {};
    
    Object.entries(this.dataCache).forEach(([timeframe, cache]) => {
      const age = now - cache.lastUpdate;
      const isValid = cache.data && age < cache.ttl;
      
      status[timeframe] = {
        hasData: !!cache.data,
        isValid: isValid,
        ageMs: age,
        ttlMs: cache.ttl,
        dataPoints: cache.data ? cache.data.length : 0
      };
    });
    
    return status;
  }

  /**
   * Get current symbol
   * @returns {string} Current trading symbol
   */
  getSymbol() {
    return this.symbol;
  }

  /**
   * Set new symbol
   * @param {string} symbol - New trading symbol
   */
  setSymbol(symbol) {
    this.symbol = symbol;
    this.clearCache(); // Clear cache when symbol changes
  }

  /**
   * Get timeframes configuration
   * @returns {Object} Timeframes configuration
   */
  getTimeframes() {
    return { ...this.timeframes };
  }

  /**
   * Get default interval
   * @returns {string} Default interval
   */
  getDefaultInterval() {
    return this.defaultInterval;
  }

  /**
   * Set default interval
   * @param {string} interval - New default interval
   */
  setDefaultInterval(interval) {
    this.defaultInterval = interval;
  }
}

// Create default instance for backward compatibility
export const binanceAPI = new BinanceAPI();

// Export individual functions for backward compatibility
export const fetchTimeframeData = (timeframe) => binanceAPI.fetchTimeframeData(timeframe);
export const getCachedData = (timeframe) => binanceAPI.getCachedData(timeframe);
export const fetchAllTimeframes = () => binanceAPI.fetchAllTimeframes();
export const fetchData = (interval, limit) => binanceAPI.fetchData(interval, limit);

// Export constants for backward compatibility
export const TIMEFRAMES = binanceAPI.getTimeframes();
export const dataCache = binanceAPI.dataCache;
