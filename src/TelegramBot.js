import { Telegraf } from "telegraf";
import dotenv from "dotenv";
import OpenAI from "openai";

dotenv.config();
// Telegram limits
const CAPTION_LIMIT = 1024; // photo caption limit
const MESSAGE_LIMIT = 4096; // regular message limit

function splitCaptionByLines(text, limit = CAPTION_LIMIT) {
  const lines = (text || '').split('\n');
  let head = '';
  let i = 0;
  for (; i < lines.length; i++) {
    const next = (head ? head + '\n' : '') + lines[i];
    if (next.length > limit) break;
    head = next;
  }
  const tail = lines.slice(i).join('\n').trim();
  return [head, tail];
}

function splitIntoChunksByLines(text, limit = MESSAGE_LIMIT) {
  const lines = (text || '').split('\n');
  const chunks = [];
  let current = '';
  for (const line of lines) {
    const candidate = (current ? current + '\n' : '') + line;
    if (candidate.length > limit) {
      if (current) chunks.push(current);
      // if a single line exceeds limit, hard-cut it
      if (line.length > limit) {
        let start = 0;
        while (start < line.length) {
          chunks.push(line.slice(start, start + limit));
          start += limit;
        }
        current = '';
      } else {
        current = line;
      }
    } else {
      current = candidate;
    }
  }
  if (current) chunks.push(current);
  return chunks;
}


/**
 * TelegramBot Class - Handles all Telegram bot interactions
 * Extracted from index.js to improve code organization and maintainability
 */
export class TelegramBot {
  constructor(options = {}) {
    // Bot Configuration
    this.token = options.token || process.env.TELEGRAM_BOT_TOKEN;
    this.chatId = options.chatId || process.env.TELEGRAM_GROUP_ID;
    this.symbol = options.symbol || process.env.SYMBOL || "ETHUSDT";

    if (!this.token) {
      throw new Error('Telegram bot token is required');
    }

    if (!this.chatId) {
      throw new Error('Telegram chat ID is required');
    }

    // Initialize Telegraf bot
    this.bot = new Telegraf(this.token);

    // Optional handler for parsed symbol command (set by app)
    this._symbolCommandHandler = null;

    // Optionally initialize OpenAI client for caption generation
    this.openai = options.openai || (process.env.OPENAI_API_KEY ? new OpenAI({ apiKey: process.env.OPENAI_API_KEY }) : null);

    // Setup error handling
    this.bot.catch((err, ctx) => {
      console.error('Telegram bot error:', err);
    });

    // Setup message handlers
    this._setupMessageHandlers();
  }

  /**
   * Allow external code to handle parsed symbol commands
   * @param {(ctx: any, symbol: string) => Promise<void>|void} handler
   */
  onSymbolCommand(handler) {
    this._symbolCommandHandler = handler;
  }

  // Internal: set up message listeners
  _setupMessageHandlers() {
    // Listen to all text messages in groups and DMs
    this.bot.on('text', async (ctx) => {
      try {
        console.log("handler chat", ctx)
        // Restrict to configured chat if provided
        if (this.chatId && String(ctx.chat?.id) !== String(this.chatId)) return;

        const text = (ctx.message?.text || '').trim();
        if (!text) return;

        const parsed = this._parseZiziCommand(text);
        if (!parsed) return; // not our command

        const { symbol, reason } = parsed;
        if (!symbol) {
          await ctx.reply(`❌ ${reason || 'Ký hiệu không hợp lệ. Dùng: zizi BTC hoặc zizi BTCUSDT'}`);
          return;
        }

        // Acknowledge
        await ctx.reply(`✅ Nhận lệnh: <b>${symbol}</b>. Đang phân tích...`, { parse_mode: 'HTML' });

        // Delegate to external handler if provided
        if (this._symbolCommandHandler) {
          await this._symbolCommandHandler(ctx, symbol);
        }
      } catch (err) {
        console.error('Telegram text handler error:', err);
        try { await ctx.reply('⚠️ Lỗi xử lý lệnh. Thử lại sau.'); } catch {}
      }
    });
  }

  // Internal: parse "zizi <symbol>" command
  _parseZiziCommand(text) {
    // Case-insensitive; allow extra spaces and punctuation
    // Examples accepted:
    //   zizi eth
    //   ZIZI ETHUSDT
    //   zizi    btc
    //   zizi ada/usdt
    //   zizi  SOL- usdt
    const m = text.match(/^\s*zizi\s+([^\s]+)\s*$/i);
    if (!m) return null;

    const raw = m[1];
    const cleaned = raw.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    if (!cleaned) return { rawInput: raw, symbol: null, reason: 'Không tìm thấy mã tiền điện tử.' };

    // If already ends with USDT, use as-is
    let symbol = cleaned;
    if (!symbol.endsWith('USDT')) {
      // Map common base assets; otherwise default append USDT
      // Allow known bases like BTC, ETH, ADA, DOT, BNB, SOL, XRP, DOGE, LINK, MATIC, AVAX, LTC, TRX, OP, ARB, ATOM, NEAR, APT, SUI, FIL, TON, ICP
      const base = symbol; // e.g., ETH
      // Basic validation: base is 2-10 letters
      if (!/^[A-Z0-9]{2,10}$/.test(base)) {
        return { rawInput: raw, symbol: null, reason: 'Mã ký hiệu không hợp lệ.' };
      }
      symbol = base + 'USDT';
    }

    // Final validation: A-Z0-9 and ends with USDT
    if (!/^[A-Z0-9]{2,10}USDT$/.test(symbol)) {
      return { rawInput: raw, symbol: null, reason: 'Cặp giao dịch không hợp lệ. Ví dụ: BTCUSDT' };
    }

    return { rawInput: raw, symbol };
  }

  /**
   * Send a photo with caption to the configured chat
   * @param {string|Buffer} photo - Photo path or buffer
   * @param {string} caption - Message caption
   * @param {Object} options - Additional options
   * @returns {Promise} Telegram API response
   */
  async sendPhoto(photo, caption = '', options = {}) {
    try {
      const defaultOptions = {
        parse_mode: 'HTML',
        ...options
      };

      const photoSource = typeof photo === 'string' ? { source: photo } : photo;

      // If caption too long, split and send remainder as a follow-up message
      let cap = caption || '';
      if (cap.length > CAPTION_LIMIT) {
        const [head, tail] = splitCaptionByLines(cap, CAPTION_LIMIT);
        const res = await this.bot.telegram.sendPhoto(
          this.chatId,
          photoSource,
          {
            caption: head,
            ...defaultOptions
          }
        );
        if (tail) {
          // Tail may still exceed message limit; split further
          const chunks = splitIntoChunksByLines(tail, MESSAGE_LIMIT);
          for (const chunk of chunks) {
            await this.bot.telegram.sendMessage(this.chatId, chunk, { parse_mode: 'HTML' });
          }
        }
        return res;
      }

      return await this.bot.telegram.sendPhoto(
        this.chatId,
        photoSource,
        {
          caption,
          ...defaultOptions
        }
      );
    } catch (error) {
      console.error('Error sending photo with HTML:', error);

      // If HTML parsing fails, try with plain text (remove all HTML tags)
      try {
        const plainCaption = caption.replace(/<[^>]*>/g, '');
        console.log('🔄 Retrying with plain text...');

        const photoSource = typeof photo === 'string' ? { source: photo } : photo;

        return await this.bot.telegram.sendPhoto(
          this.chatId,
          photoSource,
          {
            caption: plainCaption
            // No parse_mode for plain text
          }
        );
      } catch (plainError) {
        console.error('Error sending photo with plain text:', plainError);
        throw plainError;
      }
    }
  }

  /**
   * Send a text message to the configured chat
   * @param {string} message - Message text
   * @param {Object} options - Additional options
   * @returns {Promise} Telegram API response
   */
  async sendMessage(message, options = {}) {
    try {
      const defaultOptions = {
        parse_mode: 'HTML',
        ...options
      };

      return await this.bot.telegram.sendMessage(
        this.chatId,
        message,
        defaultOptions
      );
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Send error notification to the chat
   * @param {Error} error - Error object
   * @param {string} context - Error context description
   * @returns {Promise} Telegram API response
   */
  async sendErrorNotification(error, context = 'Unknown') {
    const errorMessage = `⚠️ <b>Bot Error</b>\n\n` +
      `<b>Context:</b> ${context}\n` +
      `<b>Error:</b> ${error.message}\n` +
      `<b>Time:</b> ${new Date().toISOString()}`;

    return await this.sendMessage(errorMessage);
  }

  /**
   * Send critical error notification (for system failures)
   * @param {Error} error - Error object
   * @param {string} primaryError - Primary error message
   * @returns {Promise} Telegram API response
   */
  async sendCriticalErrorNotification(error, primaryError = '') {
    const errorMessage = `⚠️ <b>Critical Bot Error</b>\n\n` +
      `All analysis systems failed. Manual intervention required.\n\n` +
      `<b>Primary Error:</b> ${primaryError || error.message}`;

    return await this.sendMessage(errorMessage);
  }

  /**
   * Create enhanced caption for multi-timeframe analysis
   * @param {Object} multiTimeframeAnalysis - Analysis data
   * @param {Object} tradingSignal - Trading signal data
   * @param {string} analysis - GPT analysis text
   * @returns {string} Formatted caption
   */
  createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis) {
    const { majorTrend, trendAlignment, confidence } = tradingSignal;

    // Trend alignment indicators
    const alignmentEmoji = {
      'BULLISH_ALIGNED': '🟢🟢🟢',
      'BEARISH_ALIGNED': '🔴🔴🔴',
      'SIDEWAYS_ALIGNED': '⚪⚪⚪',
      'MIXED': '🟢🔴⚪'
    };

    const header = `📊 <b>${this.symbol} - Multi-Timeframe Analysis</b>\n`;
    const alignment = `${alignmentEmoji[trendAlignment.alignment] || '⚪'} <b>Alignment:</b> ${trendAlignment.alignment} (${(confidence * 100).toFixed(0)}%)\n`;
    const majorTrendInfo = `📈 <b>Major Trend:</b> ${majorTrend.trend} (${(majorTrend.strength * 100).toFixed(0)}%)\n\n`;

    return header + alignment + majorTrendInfo + analysis;
  }

  /**
   * Create AI-enhanced caption for AI vision analysis
   * @param {Object} aiTradingSignal - AI trading signal data
   * @param {string} analysis - AI analysis text
   * @returns {string} Formatted caption
   */
// Tạo caption Telegram phong cách pro trader với kịch bản breakout rõ ràng
  async createAIEnhancedCaption(aiTradingSignal, analysis) {
    const {
      major_trend = {},
      trading_recommendation: tr = {},
      risk_assessment: risk = {},
      timeframe_alignment: tf = {},
      overall_assessment: overall = {},
      execution_plan: execPlan = {},
      metadata = {}
    } = aiTradingSignal || {};

    const symbol = metadata.symbol || this?.symbol || metadata?.SYMBOL || "SYMBOL";
    const timeframes = Array.isArray(metadata.timeframes_analyzed)
      ? metadata.timeframes_analyzed.join("/")
      : "4H/1H/15M";

    // Clean AI analysis notes (optional section)
    const cleanAnalysis = (analysis || "")
      .replace(/```(?:html|json)?/gi, "")
      .replace(/```/g, "")
      .replace(/\s+\n/g, "\n")
      .trim();

    // If OpenAI is not configured, fall back to manual formatter
    const apiKey = process.env.OPENAI_API_KEY;
    const canUseAI = !!apiKey && !!OpenAI;

    // Build strict prompt to preserve formatting and use all inputs
    const context = {
      symbol,
      timeframes,
      major_trend,
      trading_recommendation: tr,
      risk_assessment: risk,
      timeframe_alignment: tf,
      overall_assessment: overall,
      execution_plan: execPlan,
      metadata,
      ai_vision_insights: cleanAnalysis
    };

    const prompt = `Bạn là trader chuyên nghiệp. Hãy tạo CAPTION Telegram (HTML) theo cấu trúc cố định dưới đây, nội dung bằng tiếng Việt, ngắn gọn, chuyên nghiệp, dùng emoji phù hợp. PHẢI sử dụng toàn bộ dữ liệu cung cấp. Nếu thiếu dữ liệu, giữ nguyên N/A, KHÔNG bịa.

DỮ LIỆU:
${JSON.stringify(context, null, 2)}

YÊU CẦU ĐẦU RA (HTML, không markdown):
- Dùng đúng các thẻ: <b>, <i>, <u> và emoji.
- Bố cục bắt buộc, giữ nguyên tiêu đề/section sau:
1) Header: "🤖 <b>{{SYMBOL}} — Pro Trader Plan ({{TIMEFRAMES}})</b>" và một dòng gạch ngang.
2) Tóm tắt: 4 dòng
   • "🌊 <b>Trend:</b> {major_trend.direction} ({major_trend.strength}/10)  {alignment emoji} <i>{timeframe_alignment.alignment_type}</i>"
   • "{signal emoji} <b>Signal:</b> {trading_recommendation.action} | 🎯 <b>Entry:</b> {trading_recommendation.entry_price}"
   • "🛡️ <b>SL:</b> {stop_loss or invalidation} | 🏁 <b>TP:</b> {TP1(/TP2)} | 📊 <b>R/R:</b> {risk_reward_ratio}"
   • "{confidence emoji} <b>Confidence:</b> {overall_assessment.final_confidence}/10 <i>({overall_assessment.trade_quality})</i>"
3) Kịch bản:
   ─ Gạch ngang
   📈 <b>Scenario A — Breakout Confirmed</b>
   • Trigger: điều kiện nến, volume, PAC/EMA, RSI (tùy BUY/SELL)
   • Entry: phương án vào lệnh (break/limit/retest cụ thể)
   • SL/TP: dùng đúng giá từ dữ liệu nếu có; nếu N/A, mô tả hợp lý dựa vào risk_assessment & execution_plan
   • Quản lý: từ execution_plan.exit_strategy hoặc gợi ý phù hợp

   📉 <b>Scenario B — Fakeout/Fail</b>
   • Trigger: điều kiện thất bại/đảo chiều
   • Hành động: tránh đuổi, nêu điều kiện đảo chiều nếu có xác nhận, ưu tiên NO_TRADE nếu không rõ
   • Invalidation: nêu rõ điều kiện mất hiệu lực (giữ giá qua 1–2 nến 15M/1H…)
4) 🛰️ <b>Monitoring:</b> liệt kê execution_plan.monitoring_points hoặc gợi ý các mức cần theo dõi.
5) "📌 <b>Notes:</b> {ai_vision_insights}" nếu có ai_vision_insights.
6) Footer cảnh báo rủi ro ngắn gọn.

EMOJI QUY ƯỚC:
- Signal: STRONG_BUY=🚀, BUY=📈, HOLD=⏸️, SELL=📉, STRONG_SELL=💥, NO_TRADE=⏹️
- Alignment: FULLY_ALIGNED=✅, PARTIALLY_ALIGNED=⚠️, CONFLICTING=❌
- Confidence 1–10: 1–2=🔴, 3=🟠, 4–5=🟡, 6=🟢, 7–8=🟢🟢, 9–10=🟢🟢🟢

GIỚI HẠN:
- Ngắn gọn, rõ ràng, thực dụng; không vượt quá ~1200 ký tự.
- Chỉ trả lời bằng HTML theo bố cục trên.`;

    if (canUseAI) {
      try {
        const client = this.openai || new OpenAI({ apiKey });
        const res = await client.chat.completions.create({
          model: "gpt-4o-mini",
          messages: [
            {
              role: "system",
              content: "Bạn là trader chuyên nghiệp. Trả lời bằng HTML đẹp cho Telegram, duy trì cấu trúc cố định, dùng <b>, <i>, <u> và emoji phù hợp. Xuống dòng để rõ ý, ko để text quá dài trong 1 dòng. Không dùng markdown. Không suy diễn khi dữ liệu thiếu."
            },
            { role: "user", content: prompt }
          ],
          max_tokens: 1100,
          temperature: 0.2
        });

        let content = res.choices?.[0]?.message?.content || "";
        content = content.replace(/```(?:html)?/gi, "").replace(/```/g, "").trim();
        if (content) return content;
      } catch (err) {
        console.error("AI caption generation failed, falling back to manual:", err?.message || err);
      }
    }
    // ===== Fallback: manual formatting (original behavior) =====
  }



  /**
   * Create legacy caption for single-timeframe analysis
   * @param {string} interval - Trading interval
   * @param {string} analysis - Analysis text
   * @returns {string} Formatted caption
   */
  createLegacyCaption(interval, analysis) {
    return `📊 <b>${this.symbol} (${interval})</b>\n\n${analysis}`;
  }

  /**
   * Get bot instance for advanced usage
   * @returns {Telegraf} Telegraf bot instance
   */
  getBot() {
    return this.bot;
  }

  /**
   * Get current chat ID
   * @returns {string} Chat ID
   */
  getChatId() {
    return this.chatId;
  }

  /**
   * Set new chat ID
   * @param {string} chatId - New chat ID
   */
  setChatId(chatId) {
    this.chatId = chatId;
  }

  /**
   * Get current symbol
   * @returns {string} Current symbol
   */
  getSymbol() {
    return this.symbol;
  }

  /**
   * Set new symbol
   * @param {string} symbol - New symbol
   */
  setSymbol(symbol) {
    this.symbol = symbol;
  }

  /**
   * Start the bot (for webhook or polling mode)
   * @param {Object} options - Start options
   */
  start(options = {}) {
    if (options.webhook) {
      this.bot.launch({
        webhook: options.webhook
      });
    } else {
      this.bot.launch();
    }

    console.log('Telegram bot started');

    // Enable graceful stop
    process.once('SIGINT', () => this.bot.stop('SIGINT'));
    process.once('SIGTERM', () => this.bot.stop('SIGTERM'));
  }

  /**
   * Stop the bot
   */
  stop() {
    this.bot.stop();
    console.log('Telegram bot stopped');
  }
}

// Create default instance for backward compatibility
export const telegramBot = new TelegramBot();

// Export bot instance for backward compatibility
export const bot = telegramBot.getBot();
export const chatId = telegramBot.getChatId();
